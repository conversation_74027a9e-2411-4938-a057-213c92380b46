import React, { useState, useRef, useCallback, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react'
import { User, Crown, Eye, Camera } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useHubParticipants } from '@/hooks/collaboration-hubs'
import { useChannelMembers } from '@/hooks/chat'
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useCurrentUser } from '@/contexts/auth-context'
import type { HubParticipantResponse } from '@/lib/types/api'
import { ChatChannelResponseScope } from '@/lib/api/v1'

// Constants for better maintainability
const MENTION_INSERTION_DELAY = 50 // ms to wait before allowing new mention detection after insertion
const PARTICIPANTS_FETCH_SIZE = 100 // Number of participants to fetch
const MENTION_CHAR_REGEX = /^[a-zA-Z0-9._\s-]*$/ // Valid characters in mention text


// Enhanced participant type with disambiguation info for duplicate names
type EnhancedParticipant = HubParticipantResponse & {
  hasDuplicateName?: boolean
  disambiguationInfo?: string
}

interface MentionInputProps {
  hubId: number
  value: string // Email-based value from parent (source of truth)
  onChange: (value: string) => void // Always sends email-based value to parent
  onKeyDown?: (e: React.KeyboardEvent) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  // Context-aware mention filtering
  channelId?: number // When provided with context='chat', filters to channel participants only
  context?: 'chat' | 'post' // Determines participant scope: 'chat' = channel participants, 'post' = all hub participants
  children: (props: {
    ref: React.RefObject<HTMLDivElement>
    onChange: (e: React.FormEvent<HTMLDivElement>) => void
    onKeyDown: (e: React.KeyboardEvent) => void
    onSelect: (e: React.SyntheticEvent) => void
    onBeforeInput: (e: React.FormEvent<HTMLDivElement>) => void
    placeholder?: string
    disabled?: boolean
    className?: string
    contentEditable: boolean
    suppressContentEditableWarning: boolean
  }) => React.ReactNode
}

// Exposed methods for imperative API
export interface MentionInputRef {
  insertEmoji: (emoji: string) => void
  focus: () => void
}

interface MentionMatch {
  start: number
  end: number
  query: string
}

const MentionInputComponent = forwardRef<MentionInputRef, MentionInputProps>(
  (props, ref) => {
    const {
      hubId,
      value,
      onChange,
      onKeyDown,
      placeholder,
      disabled,
      className,
      channelId,
      context = 'post', // Default to post context for backward compatibility
      children
    } = props

    const { t, keys } = useTranslations()
  const currentUser = useCurrentUser()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [mentionMatch, setMentionMatch] = useState<MentionMatch | null>(null)
  const [justInsertedMention, setJustInsertedMention] = useState(false) // Track recent mention insertion
  const [isInitialized, setIsInitialized] = useState(false) // Track if content is initialized
  const contentEditableRef = useRef<HTMLDivElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)

  // Context-aware participant fetching
  // For chat context: fetch channel data to determine scope, then decide on participant source
  // For post context: always use all hub participants
  const shouldFetchChannelData = context === 'chat' && !!channelId

  // Always fetch hub participants for post context or as fallback for general channels
  const { data: hubParticipantsData, isLoading: isLoadingHubParticipants } = useHubParticipants(hubId, {
    enabled: !!hubId && !disabled,
    size: PARTICIPANTS_FETCH_SIZE
  })

  // Fetch channel data for chat context to determine scope and get custom channel participants
  const { data: channelData, isLoading: isLoadingChannelData } = useChannelMembers(hubId, channelId!, {
    enabled: !!hubId && !!channelId && !disabled && shouldFetchChannelData
  })

  // Determine the correct participant data source based on context and channel scope
  const participantsData = useMemo(() => {
    if (!shouldFetchChannelData) {
      // Post context: always use hub participants
      return hubParticipantsData
    }

    if (!channelData) {
      // Chat context but channel data not loaded yet: return undefined to show loading
      return undefined
    }

    // Chat context with channel data loaded
    // Check if this is a general channel (scope = 'general') or if participants array is empty/null
    // For general channels, backend returns participants = null, so we should use all hub participants
    const isGeneralChannel = channelData.scope === ChatChannelResponseScope.general ||
                             !channelData.participants ||
                             channelData.participants.length === 0

    if (isGeneralChannel) {
      // General channel: use all hub participants (same as post context)
      return hubParticipantsData
    } else {
      // Custom channel: use the specific channel participants
      return channelData
    }
  }, [shouldFetchChannelData, channelData, hubParticipantsData])

  // Unified loading state
  const isLoading = shouldFetchChannelData
    ? (isLoadingChannelData || isLoadingHubParticipants)
    : isLoadingHubParticipants

  // ARCHITECTURE: Dual-State Mention System
  // - Raw State: Email-based mentions (@<EMAIL>) as single source of truth
  // - Display State: Friendly names (@UserName) in contenteditable with styled spans
  // - Participant lookup map for bidirectional conversion

  // Create participant lookup map for email ↔ name conversion
  const participantLookup = useMemo(() => {
    const map = new Map<string, HubParticipantResponse>()
    participantsData?.content?.forEach(p => {
      if (p.email) {
        map.set(p.email.toLowerCase(), p)
      }
    })
    return map
  }, [participantsData?.content])

  const filteredParticipants = useMemo((): EnhancedParticipant[] => {
    if (!mentionMatch) return []

    const participants = participantsData?.content || []

    // Filter out current user and apply search query
    const filtered = participants.filter(p => {
      // Exclude current user from suggestions
      // Handle both internal users (with user.id) and external participants (email-only)

      // Primary check: Compare by email (works for both internal and external users)
      if (currentUser?.email && p.email) {
        const currentUserEmail = currentUser.email.toLowerCase().trim()
        const participantEmail = p.email.toLowerCase().trim()
        if (currentUserEmail === participantEmail) {
          return false
        }
      }

      // Secondary check: For internal users, also compare by ID as fallback
      // This handles edge cases where email might not match but ID does
      if (currentUser?.id && p.id && typeof currentUser.id === 'number' && typeof p.id === 'number') {
        if (currentUser.id === p.id) {
          return false
        }
      }

      const query = mentionMatch.query.toLowerCase()
      const emailPrefix = p.email?.split('@')[0]?.toLowerCase() || ''
      const name = p.name?.toLowerCase() || ''

      // Prioritize exact email prefix matches
      return emailPrefix.includes(query) || name.includes(query)
    }).sort((a, b) => {
      const query = mentionMatch.query.toLowerCase()
      const aEmailPrefix = a.email?.split('@')[0]?.toLowerCase() || ''
      const bEmailPrefix = b.email?.split('@')[0]?.toLowerCase() || ''

      // Prioritize exact email prefix matches first
      const aEmailMatch = aEmailPrefix.startsWith(query)
      const bEmailMatch = bEmailPrefix.startsWith(query)

      if (aEmailMatch && !bEmailMatch) return -1
      if (!aEmailMatch && bEmailMatch) return 1

      // Then prioritize email prefix contains over name matches
      const aEmailContains = aEmailPrefix.includes(query)
      const bEmailContains = bEmailPrefix.includes(query)

      if (aEmailContains && !bEmailContains) return -1
      if (!aEmailContains && bEmailContains) return 1

      return 0
    })

    // ARCHITECTURE: Duplicate Name Handling
    // Detect participants with duplicate display names and add disambiguation info
    const nameCount = new Map<string, number>()
    filtered.forEach(p => {
      const name = p.name || 'Unknown'
      nameCount.set(name, (nameCount.get(name) || 0) + 1)
    })

    // Add disambiguation info for participants with duplicate names
    return filtered.map(p => ({
      ...p,
      hasDuplicateName: (nameCount.get(p.name || 'Unknown') || 0) > 1,
      disambiguationInfo: (nameCount.get(p.name || 'Unknown') || 0) > 1
        ? p.email?.split('@')[0] || p.email || 'Unknown'
        : undefined
    }))
  }, [mentionMatch, participantsData?.content, currentUser?.email, currentUser?.id])



  // Convert contenteditable DOM to email-based format for backend
  const convertDisplayMentionsToEmail = useCallback((element: HTMLElement): string => {
    // Recursive function to traverse the entire DOM tree
    const traverseNode = (node: Node): string => {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent || ''
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const el = node as HTMLElement

        // Check if this is a mention token
        if (el.classList.contains('mention-token')) {
          const email = el.dataset.email
          if (email && email.trim()) {
            // Successfully found mention with email data
            return `@${email}`
          } else {
            // Mention token without proper email data - this is the bug!
            // Log for debugging and try to extract email from text content
            console.warn('Mention token found without proper email data:', {
              element: el,
              textContent: el.textContent,
              dataset: el.dataset,
              className: el.className
            })

            // Fallback: try to extract email from text content if it looks like an email
            const textContent = el.textContent || ''
            const emailMatch = textContent.match(/@([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
            if (emailMatch) {
              return `@${emailMatch[1]}`
            }

            // If no email found, this mention token is broken - return as display text
            // This should not happen in a properly functioning system
            console.error('Broken mention token - no email data available:', el)
            return textContent
          }
        } else {
          // Regular element - recursively process children
          let result = ''
          for (const child of el.childNodes) {
            result += traverseNode(child)
          }
          return result
        }
      }

      return ''
    }

    const result = traverseNode(element)

    // Debug logging to help identify conversion issues (only log if there are issues)
    try {
      const mentionTokenCount = element.querySelectorAll?.('.mention-token')?.length || 0
      if (mentionTokenCount > 0) {
        console.log('convertDisplayMentionsToEmail:', {
          input: element.innerHTML,
          output: result,
          mentionTokens: mentionTokenCount
        })
      }
    } catch (error) {
      // Ignore querySelectorAll errors in test environments
      console.warn('querySelectorAll error in convertDisplayMentionsToEmail:', error)
    }

    return result
  }, [])

  // Convert email-based content to HTML with mention spans
  const convertEmailContentToHTML = useCallback((emailContent: string): string => {
    if (!emailContent) return ''

    // First, escape HTML characters for safety
    let html = emailContent
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')

    // BULLETPROOF FIX: Process the string character by character to avoid regex issues
    // This ensures we only match complete email mentions at word boundaries
    let result = ''
    let i = 0

    while (i < html.length) {
      if (html[i] === '@') {
        // Check if this @ is at the start or after whitespace (valid mention start)
        const isValidStart = i === 0 || /\s/.test(html[i - 1])

        if (isValidStart) {
          // Try to match a complete email from this position
          const remainingText = html.slice(i + 1)
          const emailMatch = remainingText.match(/^([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)

          if (emailMatch) {
            const email = emailMatch[1]
            // Check if this email ends at a word boundary (space or end of string)
            const emailEndIndex = i + 1 + email.length
            const isValidEnd = emailEndIndex >= html.length || /\s/.test(html[emailEndIndex])

            if (isValidEnd) {
              // This is a valid complete email mention
              const participant = participantLookup.get(email.toLowerCase())
              const displayName = participant?.name || email.split('@')[0] || email

              // Use HTML entities for the span attributes to avoid XSS
              const escapedEmail = email.replace(/"/g, '&quot;')
              const escapedDisplayName = displayName.replace(/</g, '&lt;').replace(/>/g, '&gt;')

              // Debug logging for HTML conversion (only when needed)
              if (!participant) {
                console.log('Converting email to HTML mention (no participant found):', {
                  email: email,
                  displayName: displayName,
                  position: i
                })
              }

              // Add the mention span to result
              result += `<span class="mention-token" contenteditable="false" data-email="${escapedEmail}">@${escapedDisplayName}</span>`

              // Skip past the entire email
              i = emailEndIndex
              continue
            }
          }
        }
      }

      // Not a valid email mention, add the character as-is
      result += html[i]
      i++
    }

    html = result

    return html
  }, [participantLookup])

  // Cursor position management utilities
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return null

    const range = selection.getRangeAt(0)
    return {
      startContainer: range.startContainer,
      startOffset: range.startOffset,
      endContainer: range.endContainer,
      endOffset: range.endOffset
    }
  }, [])

  const restoreCursorPosition = useCallback((savedPosition: {
    startContainer: Node;
    startOffset: number;
    endContainer: Node;
    endOffset: number;
  } | null) => {
    if (!savedPosition) return

    try {
      const selection = window.getSelection()
      if (!selection) return

      const range = document.createRange()
      range.setStart(savedPosition.startContainer, savedPosition.startOffset)
      range.setEnd(savedPosition.endContainer, savedPosition.endOffset)

      selection.removeAllRanges()
      selection.addRange(range)
    } catch (_error) {
      // Ignore errors if nodes are no longer valid
    }
  }, [])

  // CURSOR PROTECTION: Ensure cursor is never inside mention tokens
  const ensureCursorOutsideMentionToken = useCallback(() => {
    if (!contentEditableRef.current) return false

    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return false

    const range = selection.getRangeAt(0)
    const { startContainer } = range

    // Helper function to find mention token ancestor
    const findMentionTokenAncestor = (node: Node): Element | null => {
      let current = node
      while (current && current !== contentEditableRef.current) {
        if (current.nodeType === Node.ELEMENT_NODE &&
            (current as Element).classList.contains('mention-token')) {
          return current as Element
        }
        current = current.parentNode!
      }
      return null
    }

    // Check if cursor is inside a mention token
    const mentionAncestor = findMentionTokenAncestor(startContainer)
    if (mentionAncestor) {
      console.log('Cursor detected inside mention token, moving to safe position')

      // Find or create a space after the mention token
      let spaceNode = mentionAncestor.nextSibling

      // If there's no space after the mention, create one
      if (!spaceNode || spaceNode.nodeType !== Node.TEXT_NODE || !spaceNode.textContent?.startsWith(' ')) {
        const newSpaceNode = document.createTextNode(' ')
        const parentNode = mentionAncestor.parentNode!

        if (spaceNode) {
          parentNode.insertBefore(newSpaceNode, spaceNode)
        } else {
          parentNode.appendChild(newSpaceNode)
        }
        spaceNode = newSpaceNode
        console.log('Created space after mention token')
      }

      // Move cursor to after the space
      const newRange = document.createRange()
      if (spaceNode.textContent && spaceNode.textContent.length > 0) {
        newRange.setStart(spaceNode, 1) // Position after the space character
      } else {
        newRange.setStartAfter(spaceNode)
      }
      newRange.collapse(true)

      selection.removeAllRanges()
      selection.addRange(newRange)

      console.log('Moved cursor to safe position after mention token')
      return true // Cursor was moved
    }

    return false // Cursor was already in a safe position
  }, [])

  // MENTION SPACE PROTECTION: Ensure all mentions have trailing spaces
  const ensureMentionsHaveTrailingSpaces = useCallback(() => {
    if (!contentEditableRef.current) return false

    const element = contentEditableRef.current
    let mentionTokens: NodeListOf<Element>

    try {
      mentionTokens = element.querySelectorAll('.mention-token')
    } catch (error) {
      // Handle querySelectorAll errors in test environments
      console.warn('querySelectorAll error in ensureMentionsHaveTrailingSpaces:', error)
      return false
    }

    let modified = false

    mentionTokens.forEach(mentionToken => {
      const nextSibling = mentionToken.nextSibling

      // Check if there's no space after the mention or if it's at the end
      if (!nextSibling ||
          (nextSibling.nodeType === Node.TEXT_NODE && !nextSibling.textContent?.startsWith(' '))) {

        // Add a space after the mention
        const spaceNode = document.createTextNode(' ')
        const parentNode = mentionToken.parentNode!

        if (nextSibling) {
          parentNode.insertBefore(spaceNode, nextSibling)
        } else {
          parentNode.appendChild(spaceNode)
        }

        modified = true
        console.log('Added trailing space after mention token')
      }
    })

    return modified
  }, [])

  // Insert emoji at current cursor position in contenteditable
  const insertEmojiAtCursor = useCallback((emoji: string) => {
    if (!contentEditableRef.current || disabled) return

    const element = contentEditableRef.current
    const selection = window.getSelection()

    // Focus the element first to ensure proper selection handling
    element.focus()

    if (!selection) {
      console.warn('No selection available for emoji insertion')
      return
    }

    // If no range exists, create one at the end of the content
    if (selection.rangeCount === 0) {
      const range = document.createRange()
      range.selectNodeContents(element)
      range.collapse(false) // Collapse to end
      selection.addRange(range)
    }

    const range = selection.getRangeAt(0)

    try {
      // MENTION-AWARE EMOJI INSERTION
      // This logic ensures emojis are never inserted inside mention tokens

      // Delete any selected content first
      if (!range.collapsed) {
        range.deleteContents()
      }

      // Check if cursor is inside or adjacent to a mention token
      const { startContainer, startOffset } = range
      let insertAfterNode = null

      // Helper function to check if a node is a mention token
      const isMentionToken = (node: Node | null): boolean => {
        return node?.nodeType === Node.ELEMENT_NODE &&
               (node as Element).classList.contains('mention-token')
      }

      // Helper function to find the closest mention token ancestor
      const findMentionTokenAncestor = (node: Node): Element | null => {
        let current = node
        while (current && current !== element) {
          if (current.nodeType === Node.ELEMENT_NODE &&
              (current as Element).classList.contains('mention-token')) {
            return current as Element
          }
          current = current.parentNode!
        }
        return null
      }

      // Case 1: Cursor is inside a mention token - insert after the mention token
      const mentionAncestor = findMentionTokenAncestor(startContainer)
      if (mentionAncestor) {
        console.log('Cursor inside mention token, inserting after mention')
        insertAfterNode = mentionAncestor
      }
      // Case 2: Cursor is in a text node right after a mention token
      else if (startContainer.nodeType === Node.TEXT_NODE && startOffset === 0) {
        const previousSibling = startContainer.previousSibling
        if (isMentionToken(previousSibling)) {
          console.log('Cursor at start of text node after mention, inserting after mention')
          insertAfterNode = previousSibling
        }
      }
      // Case 3: Cursor is directly in the contenteditable element
      else if (startContainer === element && startOffset > 0) {
        const nodeBeforeCursor = startContainer.childNodes[startOffset - 1]
        if (isMentionToken(nodeBeforeCursor)) {
          console.log('Cursor directly after mention in element, inserting after mention')
          insertAfterNode = nodeBeforeCursor
        }
      }

      // Create emoji text node
      const emojiNode = document.createTextNode(emoji)

      if (insertAfterNode) {
        // Insert emoji after the mention token
        const parentNode = insertAfterNode.parentNode!
        const nextSibling = insertAfterNode.nextSibling

        if (nextSibling) {
          parentNode.insertBefore(emojiNode, nextSibling)
        } else {
          parentNode.appendChild(emojiNode)
        }

        // Position cursor after the emoji
        range.setStartAfter(emojiNode)
        range.collapse(true)
        console.log('Emoji inserted after mention token:', { emoji })
      } else {
        // Normal insertion at cursor position
        range.insertNode(emojiNode)

        // Position cursor after the emoji
        range.setStartAfter(emojiNode)
        range.collapse(true)
        console.log('Emoji inserted at cursor position:', { emoji })
      }

      // Update the selection
      selection.removeAllRanges()
      selection.addRange(range)

    } catch (error) {
      console.error('Error inserting emoji with mention-aware method, using fallback:', error)

      // Fallback: append to end if insertion fails
      const emojiNode = document.createTextNode(emoji)
      element.appendChild(emojiNode)

      // Position cursor after the emoji
      const newRange = document.createRange()
      newRange.setStartAfter(emojiNode)
      newRange.collapse(true)
      selection.removeAllRanges()
      selection.addRange(newRange)

      console.log('Emoji inserted via fallback:', { emoji, position: 'at end' })
    }

    // Convert updated DOM to email format and notify parent
    const newEmailContent = convertDisplayMentionsToEmail(element)
    onChange(newEmailContent)

    console.log('Emoji insertion complete:', { emoji, newContent: newEmailContent })
  }, [disabled, convertDisplayMentionsToEmail, onChange])

  // Focus the contenteditable element
  const focusInput = useCallback(() => {
    console.log('MentionInput focusInput called', {
      hasRef: !!contentEditableRef.current,
      disabled,
      activeElement: document.activeElement?.tagName
    });

    if (contentEditableRef.current && !disabled) {
      // Force blur any currently focused element first
      if (document.activeElement && document.activeElement !== document.body) {
        console.log('Force blurring current element:', document.activeElement.tagName);
        (document.activeElement as HTMLElement).blur();
      }

      // Small delay to ensure blur takes effect
      setTimeout(() => {
        if (contentEditableRef.current) {
          console.log('Focusing contenteditable element...');
          contentEditableRef.current.focus();

          // Move cursor to end of content
          const selection = window.getSelection();
          if (selection) {
            const range = document.createRange();
            range.selectNodeContents(contentEditableRef.current);
            range.collapse(false); // Collapse to end
            selection.removeAllRanges();
            selection.addRange(range);
            console.log('Cursor positioned at end of content');
          }

          // Verify focus was set
          setTimeout(() => {
            console.log('Focus verification - active element:', document.activeElement?.tagName, document.activeElement?.className);
            console.log('Is contenteditable focused?', document.activeElement === contentEditableRef.current);
          }, 10);
        }
      }, 10);
    } else {
      console.log('Cannot focus - missing ref or disabled', { hasRef: !!contentEditableRef.current, disabled });
    }
  }, [disabled]);

  // Expose imperative API for emoji insertion and focus
  useImperativeHandle(ref, () => ({
    insertEmoji: insertEmojiAtCursor,
    focus: focusInput
  }), [insertEmojiAtCursor, focusInput])

  // Initialize content when value changes (only for initial load or external updates)
  useEffect(() => {
    if (!contentEditableRef.current) return

    const element = contentEditableRef.current
    const currentContent = convertDisplayMentionsToEmail(element)

    // Only update if the content is different and we haven't initialized yet
    // or if this is an external update (value changed but not from user input)
    // Special case: Always clear when value is empty (for proper input clearing)
    if (currentContent !== value && (!isInitialized || element.textContent === '' || value === '')) {
      const savedPosition = saveCursorPosition()
      element.innerHTML = convertEmailContentToHTML(value)

      // Ensure all mentions have trailing spaces after content is set
      ensureMentionsHaveTrailingSpaces()

      restoreCursorPosition(savedPosition)
      setIsInitialized(true)
    }
  }, [value, convertEmailContentToHTML, convertDisplayMentionsToEmail, saveCursorPosition, restoreCursorPosition, isInitialized, ensureMentionsHaveTrailingSpaces])

  // Manage popper visibility based on mention match and available participants
  useEffect(() => {
    // Only show popper if there's a mention match AND there are participants to show
    // AND we haven't just inserted a mention (to prevent immediate reopening)
    const shouldShowPopper = !!mentionMatch && filteredParticipants.length > 0 && !justInsertedMention
    setIsOpen(shouldShowPopper)

    // Debug logging for dropdown state
    if (mentionMatch && justInsertedMention) {
      console.log('Dropdown blocked due to recent mention insertion')
    }
  }, [mentionMatch, filteredParticipants.length, justInsertedMention])

  // Get cursor position in text content (ignoring HTML tags)
  const getCursorPositionInText = useCallback((element: HTMLElement, range: Range): number => {
    let position = 0
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    )

    let node
    while ((node = walker.nextNode())) {
      if (node === range.startContainer) {
        return position + range.startOffset
      }
      position += node.textContent?.length || 0
    }

    return position
  }, [])

  // Find mention match in contenteditable content
  const findMentionMatchInContentEditable = useCallback((element: HTMLElement, cursor: number): MentionMatch | null => {
    // BUGFIX: Check if cursor is positioned right after a completed mention token
    // This prevents the dropdown from incorrectly appearing when user deletes space after a mention
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const { startContainer, startOffset } = range

      // Helper function to check if a node is a mention token
      const isMentionToken = (node: Node | null): boolean => {
        return node?.nodeType === Node.ELEMENT_NODE &&
               (node as Element).classList.contains('mention-token')
      }

      // Case 1: Cursor is in a text node - check if previous sibling is a mention token
      if (startContainer.nodeType === Node.TEXT_NODE) {
        // If cursor is at the beginning of a text node, check previous sibling
        if (startOffset === 0 && isMentionToken(startContainer.previousSibling)) {
          console.log('Cursor detected right after mention token (text node case)')
          return null
        }
      }

      // Case 2: Cursor is directly in the contenteditable element
      else if (startContainer === element) {
        // Check if the node right before cursor position is a mention token
        if (startOffset > 0) {
          const nodeBeforeCursor = startContainer.childNodes[startOffset - 1]
          if (isMentionToken(nodeBeforeCursor)) {
            console.log('Cursor detected right after mention token (element case)')
            return null
          }
        }
      }
    }

    const textContent = element.textContent || ''
    const before = textContent.slice(0, cursor)

    // Find the last @ symbol before cursor
    const lastAtIndex = before.lastIndexOf('@')
    if (lastAtIndex === -1) return null

    // Get text after the @ symbol up to cursor
    const afterAt = before.slice(lastAtIndex + 1)

    // Check if this looks like a complete email address
    const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
    const textAfterAt = textContent.slice(lastAtIndex + 1)
    if (emailPattern.test(textAfterAt)) {
      // This is a complete email address, not a new mention
      return null
    }

    // Only exclude mentions that end with a space (user pressed space after completing a mention)
    if (afterAt.endsWith(' ')) {
      return null
    }

    // Match valid mention characters (letters, numbers, dots, underscores, spaces for multi-word names)
    if (MENTION_CHAR_REGEX.test(afterAt)) {
      return {
        start: lastAtIndex,
        end: cursor,
        query: afterAt
      }
    }

    return null
  }, [])

  // Handle click outside to close popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen &&
          popoverRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          contentEditableRef.current &&
          !contentEditableRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-600" />
      case 'reviewer':
        return <Eye className="h-3 w-3 text-blue-600" />
      case 'reviewer_creator':
        return <Camera className="h-3 w-3 text-purple-600" />
      case 'content_creator':
        return <User className="h-3 w-3 text-green-600" />
      default:
        return <User className="h-3 w-3 text-gray-600" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      default:
        return role
    }
  }

  // Handle contenteditable input events
  const handleContentEditableInput = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    const element = e.currentTarget

    // CURSOR PROTECTION: First ensure cursor is not inside a mention token
    const cursorWasMoved = ensureCursorOutsideMentionToken()

    // If cursor was moved, we need to update the element reference and reprocess
    if (cursorWasMoved) {
      // Small delay to let the cursor position settle
      setTimeout(() => {
        if (contentEditableRef.current) {
          const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
          onChange(newEmailContent)
        }
      }, 0)
      return
    }

    // Convert DOM to email format and notify parent
    const emailContent = convertDisplayMentionsToEmail(element)
    onChange(emailContent)

    // Update mention detection based on cursor position
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const cursor = getCursorPositionInText(element, range)
      const match = findMentionMatchInContentEditable(element, cursor)

      // If user typed a space and dropdown is open, close it (completed mention)
      if (match === null && isOpen) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
        return
      }

      // If we just inserted a mention, don't immediately reopen dropdown
      if (justInsertedMention) {
        console.log('Mention just inserted, skipping mention detection')
        return
      }

      // Only update mention match if it's different from the current one
      if (!mentionMatch ||
          !match ||
          match.start !== mentionMatch.start ||
          match.end !== mentionMatch.end ||
          match.query !== mentionMatch.query) {
        setMentionMatch(match)
        setSelectedIndex(0)
      }
    }
  }, [onChange, convertDisplayMentionsToEmail, getCursorPositionInText, findMentionMatchInContentEditable, isOpen, justInsertedMention, mentionMatch, ensureCursorOutsideMentionToken])





  // Insert mention token as styled span
  const insertMentionToken = useCallback((participant: HubParticipantResponse) => {
    if (!mentionMatch || !contentEditableRef.current) return

    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const element = contentEditableRef.current

    // Validate participant has email - this is critical for the dual-state system
    if (!participant.email || !participant.email.trim()) {
      console.error('Cannot insert mention token - participant missing email:', participant)
      return
    }

    // SIMPLE AND RELIABLE FIX: Use direct DOM replacement to avoid position mapping issues

    // 1. Find the text node containing the @ symbol and replace the mention query directly

    // 2. Find the text node that contains our @ symbol
    let targetTextNode = null
    let nodeOffset = 0

    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    )

    let currentPos = 0
    let node
    while ((node = walker.nextNode())) {
      const nodeLength = node.textContent?.length || 0
      if (currentPos <= mentionMatch.start && currentPos + nodeLength > mentionMatch.start) {
        targetTextNode = node
        nodeOffset = mentionMatch.start - currentPos
        break
      }
      currentPos += nodeLength
    }

    if (targetTextNode && targetTextNode.nodeType === Node.TEXT_NODE) {
      // 3. Create the mention span
      const mentionSpan = document.createElement('span')
      mentionSpan.contentEditable = 'false'
      mentionSpan.className = 'mention-token'
      mentionSpan.dataset.email = participant.email.trim()
      mentionSpan.textContent = `@${participant.name || participant.email.split('@')[0] || 'Unknown'}`

      // 4. Split the text node and insert the mention
      const beforeText = targetTextNode.textContent?.slice(0, nodeOffset) || ''
      const afterText = targetTextNode.textContent?.slice(nodeOffset + (mentionMatch.end - mentionMatch.start)) || ''

      const parentNode = targetTextNode.parentNode
      if (parentNode) {
        // Create new nodes - ensure space is always present
        const beforeTextNode = beforeText ? document.createTextNode(beforeText) : null
        const spaceNode = document.createTextNode(' ')
        const afterTextNode = afterText ? document.createTextNode(afterText) : null

        // Replace the original text node
        if (beforeTextNode) {
          parentNode.insertBefore(beforeTextNode, targetTextNode)
        }
        parentNode.insertBefore(mentionSpan, targetTextNode)
        parentNode.insertBefore(spaceNode, targetTextNode)
        if (afterTextNode) {
          parentNode.insertBefore(afterTextNode, targetTextNode)
        }
        parentNode.removeChild(targetTextNode)

        // 5. Position cursor after the space - more robust positioning
        const newRange = document.createRange()
        try {
          // Position cursor at the end of the space text node (after the space character)
          newRange.setStart(spaceNode, 1)
          newRange.collapse(true)
          selection.removeAllRanges()
          selection.addRange(newRange)
          console.log('Cursor positioned after space character in text node')
        } catch (error) {
          // Fallback: position after the space node
          console.warn('Failed to position cursor in space text node, using fallback:', error)
          newRange.setStartAfter(spaceNode)
          newRange.collapse(true)
          selection.removeAllRanges()
          selection.addRange(newRange)
          console.log('Cursor positioned after space node (fallback)')
        }
      }
    }

    // 6. Get the final email content from the updated DOM
    const newEmailContent = convertDisplayMentionsToEmail(element)

    // Debug logging for mention creation
    console.log('Creating mention token:', {
      participant: participant,
      email: participant.email,
      displayName: participant.name,
      mentionMatch: mentionMatch,
      newEmailContent: newEmailContent
    })



    // 5. Notify parent with the new email content
    onChange(newEmailContent)

    // FORCE CLOSE dropdown and mark that we just inserted a mention
    setIsOpen(false)
    setMentionMatch(null)
    setSelectedIndex(0)
    setJustInsertedMention(true)

    // Clear the flag after a short delay to allow normal mention detection to resume
    setTimeout(() => {
      setJustInsertedMention(false)
      console.log('Mention insertion cooldown ended, mention detection resumed')
    }, MENTION_INSERTION_DELAY)

    // Additional safety: force close dropdown after a brief delay to handle any race conditions
    setTimeout(() => {
      setIsOpen(false)
      setMentionMatch(null)
    }, 10)
  }, [mentionMatch, convertDisplayMentionsToEmail, onChange, convertEmailContentToHTML])



  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle backspace/delete for mention tokens when dropdown is not open
    if (!isOpen && (e.key === 'Backspace' || e.key === 'Delete')) {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0 && contentEditableRef.current) {
        const range = selection.getRangeAt(0)
        const { startContainer, startOffset } = range

        // Handle backspace at the beginning of text nodes (cursor right after mention token)
        if (e.key === 'Backspace' && startContainer.nodeType === Node.TEXT_NODE && startOffset === 0) {
          const previousSibling = startContainer.previousSibling
          if (previousSibling && previousSibling.nodeType === Node.ELEMENT_NODE) {
            const element = previousSibling as Element
            if (element.classList.contains('mention-token')) {
              e.preventDefault()
              // Remove the mention token
              element.remove()
              // Update the parent with new content
              const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
              onChange(newEmailContent)
              console.log('Mention token deleted via backspace')
              return
            }
          }
        }

        // Handle delete at the end of text nodes (cursor right before mention token)
        if (e.key === 'Delete' && startContainer.nodeType === Node.TEXT_NODE) {
          const textContent = startContainer.textContent || ''
          if (startOffset === textContent.length) {
            const nextSibling = startContainer.nextSibling
            if (nextSibling && nextSibling.nodeType === Node.ELEMENT_NODE) {
              const element = nextSibling as Element
              if (element.classList.contains('mention-token')) {
                e.preventDefault()
                // Remove the mention token
                element.remove()
                // Update the parent with new content
                const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
                onChange(newEmailContent)
                console.log('Mention token deleted via delete key')
                return
              }
            }
          }
        }

        // Handle backspace when cursor is directly in contenteditable element
        if (e.key === 'Backspace' && startContainer === contentEditableRef.current && startOffset > 0) {
          const nodeBeforeCursor = startContainer.childNodes[startOffset - 1]
          if (nodeBeforeCursor && nodeBeforeCursor.nodeType === Node.ELEMENT_NODE) {
            const element = nodeBeforeCursor as Element
            if (element.classList.contains('mention-token')) {
              e.preventDefault()
              // Remove the mention token
              element.remove()
              // Update the parent with new content
              const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
              onChange(newEmailContent)
              console.log('Mention token deleted via backspace (direct element case)')
              return
            }
          }
        }
      }
    }

    if (isOpen && filteredParticipants.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredParticipants.length)
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredParticipants.length) % filteredParticipants.length)
          break
        case 'Enter':
        case 'Tab':
          e.preventDefault()
          insertMentionToken(filteredParticipants[selectedIndex])
          break
        case 'Escape':
          e.preventDefault()
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
          break
        case ' ':
          // Space key - close the popup if we're at the end of a mention
          if (mentionMatch) {
            setIsOpen(false)
            setMentionMatch(null)
            setSelectedIndex(0)
          }
          onKeyDown?.(e)
          break
        default:
          onKeyDown?.(e)
      }
    } else {
      onKeyDown?.(e)
    }
  }, [isOpen, filteredParticipants, selectedIndex, mentionMatch, onKeyDown, insertMentionToken, convertDisplayMentionsToEmail, onChange])

  const handleParticipantSelect = useCallback((e: React.MouseEvent, p: HubParticipantResponse) => {
    e.preventDefault()
    e.stopPropagation()
    insertMentionToken(p)
  }, [insertMentionToken])

  const handleSelect = useCallback(() => {
    // Don't interfere with mention selection when popup is open
    if (isOpen) {
      return
    }

    // CURSOR PROTECTION: Ensure cursor is not inside mention token
    setTimeout(() => {
      const cursorWasMoved = ensureCursorOutsideMentionToken()

      // Update mention match based on cursor position
      if (!contentEditableRef.current) return

      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const cursor = getCursorPositionInText(contentEditableRef.current, range)
        const match = findMentionMatchInContentEditable(contentEditableRef.current, cursor)

        if (match) {
          setMentionMatch(match)
          setSelectedIndex(0)
        } else if (mentionMatch) {
          setMentionMatch(null)
          setSelectedIndex(0)
        }
      }

      // If cursor was moved, update parent with new content
      if (cursorWasMoved && contentEditableRef.current) {
        const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
        onChange(newEmailContent)
      }
    }, 0)
  }, [getCursorPositionInText, findMentionMatchInContentEditable, mentionMatch, isOpen, ensureCursorOutsideMentionToken, convertDisplayMentionsToEmail, onChange])

  // Handle beforeinput to prevent text insertion inside mention tokens
  const handleBeforeInput = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    // Check if cursor is inside a mention token and prevent input
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const { startContainer } = range

      // Helper function to find mention token ancestor
      const findMentionTokenAncestor = (node: Node): Element | null => {
        let current = node
        while (current && current !== contentEditableRef.current) {
          if (current.nodeType === Node.ELEMENT_NODE &&
              (current as Element).classList.contains('mention-token')) {
            return current as Element
          }
          current = current.parentNode!
        }
        return null
      }

      const mentionAncestor = findMentionTokenAncestor(startContainer)
      if (mentionAncestor) {
        console.log('Preventing input inside mention token')
        e.preventDefault()

        // Move cursor to after the mention and allow the input there
        ensureCursorOutsideMentionToken()
        return
      }
    }
  }, [ensureCursorOutsideMentionToken])

  return (
    <div className={cn("relative text-sm", className)}>
      {children({
        ref: contentEditableRef,
        onChange: handleContentEditableInput,
        onKeyDown: handleKeyDown,
        onSelect: handleSelect,
        onBeforeInput: handleBeforeInput,
        placeholder,
        disabled,
        className,
        contentEditable: !disabled,
        suppressContentEditableWarning: true
      })}



      {isOpen && (
        <div
          ref={popoverRef}
          className="absolute z-50 bottom-full mb-1 bg-popover border border-border rounded-md shadow-md w-auto min-w-[16rem] max-w-[min(90vw,40rem)]"
        >
          <div className="p-2">
            {isLoading ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.loadingParticipants)}
              </div>
            ) : (
              <ScrollArea className="max-h-64 max-w-full">
                {filteredParticipants.map((p, index) => (
                  <div
                    key={p.id}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleParticipantSelect(e, p)
                    }}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer rounded-md hover:bg-accent",
                      index === selectedIndex && "bg-accent"
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(p.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm whitespace-normal break-words">
                          {p.name || 'Unknown'}
                          {p.hasDuplicateName && p.disambiguationInfo && (
                            <span className="text-muted-foreground font-normal ml-1">
                              ({p.disambiguationInfo})
                            </span>
                          )}
                        </p>
                        {p.role && <span>{getRoleIcon(p.role)}</span>}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground break-all">
                          {p.email}
                        </p>
                        {p.role && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {getRoleLabel(p.role)}
                          </Badge>
                        )}
                        {p.isExternal && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            External
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            )}
          </div>
        </div>
      )}
    </div>
  )
})

// Export memoized component with proper comparison
export const MentionInput = React.memo(MentionInputComponent, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.hubId === nextProps.hubId &&
    prevProps.value === nextProps.value &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.className === nextProps.className &&
    prevProps.channelId === nextProps.channelId &&
    prevProps.context === nextProps.context &&
    prevProps.onChange === nextProps.onChange &&
    prevProps.onKeyDown === nextProps.onKeyDown
  )
})
